<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\pattern\BonusHelper;
use App\Services\pattern\MemberInstance;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;

class RecommendBonusDistributionTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 設定測試資料庫連接
        config(['database.connections.main_db' => config('database.connections.mysql')]);
    }

    /**
     * 測試推薦獎勵分配給全體合夥人的邏輯
     */
    public function test_recommend_bonus_distribution_to_partners()
    {
        // 建立測試用的合夥人等級資料
        DB::table('partner_level')->insert([
            ['id' => 1, 'name' => '任督', 'contribution' => 1320, 'orderform_ad_weight' => 10, 'ratio' => 1.0],
            ['id' => 2, 'name' => '中脈', 'contribution' => 3740, 'orderform_ad_weight' => 20, 'ratio' => 1.2],
            ['id' => 3, 'name' => '法身', 'contribution' => 11000, 'orderform_ad_weight' => 30, 'ratio' => 1.5],
        ]);

        // 建立測試用的會員等級資料
        DB::table('vip_type')->insert([
            ['id' => 1, 'vip_name' => '任督', 'burn_cv' => 1320],
            ['id' => 2, 'vip_name' => '中脈', 'burn_cv' => 3740],
            ['id' => 3, 'vip_name' => '法身', 'burn_cv' => 11000],
        ]);

        // 建立測試用的回饋模組資料
        DB::table('bonus_model')->insert([
            'id' => 1,
            'name' => '測試回饋模組',
            'normal_recommend' => 20, // 推廣獎勵20%
            'use_partner_mode' => 0,
        ]);

        // 建立測試用的會員資料
        DB::connection('main_db')->table('account')->insert([
            [
                'id' => 1,
                'number' => 'TEST001',
                'partner_level_id' => 1,
                'vip_type' => 1,
                'increasing_limit_invest' => 1000,
                'auto_partner' => 1,
            ],
            [
                'id' => 2,
                'number' => 'TEST002',
                'partner_level_id' => 2,
                'vip_type' => 2,
                'increasing_limit_invest' => 2000,
                'auto_partner' => 1,
            ],
            [
                'id' => 3,
                'number' => 'TEST003',
                'partner_level_id' => 3,
                'vip_type' => 3,
                'increasing_limit_invest' => 3000,
                'auto_partner' => 1,
            ],
            [
                'id' => 4, // 購買者
                'number' => 'BUYER001',
                'partner_level_id' => 0,
                'vip_type' => 0,
                'increasing_limit_invest' => 0,
                'auto_partner' => 1,
            ],
        ]);

        // 建立BonusHelper實例
        $bonusHelper = new BonusHelper();

        // 模擬商品資料
        $product = [
            'bonus_model_id' => 1,
            'price_cv' => 6600, // 中脈優惠價 6600 CV
            'deduct_invest' => 0,
            'deduct_consumption' => 0,
        ];

        $buyer_id = 4;
        $share_cv = 1980; // 假設分享CV金額為1980 (6600 * 30%)

        // 執行推薦獎勵分配
        $recommend_total_cv = $bonusHelper->distribute_recommend_bonus_to_partners($product, $buyer_id, $share_cv);

        // 驗證推薦獎勵總額
        $expected_total = $share_cv * 0.2; // 20%推薦獎勵
        $this->assertEquals($expected_total, $recommend_total_cv);

        // 驗證各合夥人獲得的獎勵
        // 權重計算：任督(10) + 中脈(20) + 法身(30) = 60
        // 任督應得：396 * (10/60) * 燒傷比例
        // 中脈應得：396 * (20/60) * 燒傷比例  
        // 法身應得：396 * (30/60) * 燒傷比例

        $this->assertTrue($recommend_total_cv > 0);
    }

    /**
     * 測試燒傷機制
     */
    public function test_burn_cv_mechanism()
    {
        // 建立BonusHelper實例
        $bonusHelper = new BonusHelper();

        // 模擬高CV商品但會員等級較低的情況
        $product = [
            'price_cv' => 11000, // 法身級別的CV
            'deduct_invest' => 0,
            'deduct_consumption' => 0,
        ];

        // 測試中脈級別會員的燒傷
        $member_id = 2; // 中脈級別會員
        $share_cv_burned = $bonusHelper->count_share_cv_vip_type($product, $member_id);

        // 中脈級別的燒傷CV應該是3740，而不是11000
        $expected_burned_cv = 3740 * 0.3; // 假設cv_rate為30%
        $this->assertEquals($expected_burned_cv, $share_cv_burned);
    }
}
