<?php

require_once 'vendor/autoload.php';

use App\Services\pattern\OrderHelper;

// 測試取得訂單分類資訊
try {
    // 測試一個訂單ID（你需要替換成實際存在的訂單ID）
    $orderform_id = 1;
    
    echo "=== 測試訂單分類資訊功能 ===\n";
    
    // 測試 1: 取得包含分類資訊的商品資料
    echo "\n1. 測試 get_orderform_products 包含分類資訊:\n";
    $products_with_category = OrderHelper::get_orderform_products([$orderform_id], '-2', ['include_category_info' => true]);
    
    foreach ($products_with_category as $product) {
        echo sprintf(
            "商品ID: %s, 分類: %s (%s), 金額: %s\n",
            $product['info_id'],
            $product['category_type'] ?? 'N/A',
            ($product['category_type'] ?? 0) == 1 ? '課程' : '商城',
            $product['total'] ?? 0
        );
    }
    
    // 測試 2: 取得訂單整體分類資訊
    echo "\n2. 測試 getOrderCategoryInfo:\n";
    $order_category_info = OrderHelper::getOrderCategoryInfo($orderform_id);
    
    echo "主要分類: " . ($order_category_info['primary_category'] == 1 ? '課程' : '商城') . "\n";
    echo "商城商品: " . $order_category_info['category_summary']['mall']['count'] . " 件, 金額: " . $order_category_info['category_summary']['mall']['amount'] . "\n";
    echo "課程商品: " . $order_category_info['category_summary']['course']['count'] . " 件, 金額: " . $order_category_info['category_summary']['course']['amount'] . "\n";
    
    echo "\n=== 測試完成 ===\n";
    
} catch (Exception $e) {
    echo "錯誤: " . $e->getMessage() . "\n";
    echo "請確認:\n";
    echo "1. 資料庫連線設定正確\n";
    echo "2. 訂單ID存在\n";
    echo "3. productinfo 表有 category_type 欄位\n";
}
