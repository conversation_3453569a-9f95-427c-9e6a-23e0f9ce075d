<?php

namespace App\Services\pattern;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
//Photonic Class
use App\Services\CommonService;
use App\Services\pattern\HelperService;
use App\Services\pattern\MemberInstance;
use App\Services\pattern\PointRecords;
use App\Services\pattern\BonusSettingHelper;

class BonusHelper
{
    private MemberInstance $MemberInstance;
    private PointRecords $PointRecords;
    private $now_time = null;         /*處理回饋時間(timestamp)*/
    private $now_time_f = null;       /*處理回饋時間(Y-m-d H:i:s)*/
    private $buyer_id = 0;            /*處理訂單的購買者*/
    private $buyer_topline_id = 0;    /*處理訂單的購買者的推薦者*/
    private $orderform_id = 0;        /*處理訂單的ID*/
    private $order_number = '';       /*處理訂單的訂單編號*/

    private $pi_pool_change = 0;      /*總結本次總計「增值積分資金池」異動CV金額*/
    private $pi_change = 0;           /*總結本次總計「增值積分」異動CV金額*/

    private $user_cal = [];                   /*回饋對象計算紀錄(array,以 user_id 為key對應 user_set)*/

    private static $user_set = [
        'data' => [],                           // 回饋【前】原始會員資料(欄位請參照 function set_user_column_data 內容)
        'total_invest' => 0.0,                  // 累積本次線上消費回饋投資金額(最後一次算出:final_points_record, final_increasing_limit_record, 並調整 final_user_status)
        'total_available_cv' => 0.0,            // 累積本次線上消費回饋應分潤金額(最後一次算出:final_points_increasing_record, 並調整 final_user_status)
        'increasing_limit_consumption' => 0.0,  // 累積本次線上消費回饋消費圓滿點數(最後一次算出:final_increasing_limit_record, 並調整 final_user_status)
        'increasing_limit_other' => 0.0,        // 累積本次線上消費回饋其他圓滿點數(最後一次算出:final_increasing_limit_record, 並調整 final_user_status)
        'price_supplier' => 0.0,                // 累積本次線上消費回饋供應商回饋金額
        'check_num' => 0,                       // 需檢查增值的金額(原積分價值差+新積分價值，並依 final_user_status 計算結果進行「積分自動轉換」)
    ];

    private $final_user_status = [];              /*回饋【後】會員狀態(array,以 user_id 為key對應 最終應調整的會員狀態(欄位請參照 function init_user_set 內容))*/
    private $final_points_record = [];            /*總結本次應發送的「現金積分」紀錄*/
    private $final_points_increasing_record = []; /*總結本次應發送的「增值積分」的紀錄*/
    private $final_increasing_limit_record = [];  /*總結本次應發送的「圓滿點數」的紀錄*/
    private $auto_invest = [];                    /*自動投資紀錄(會員id=>金額)(處理回饋後因達增值上限而產生)*/

    public $bonus_setting = [];                 /*回饋設定(以id為key)*/
    public $arr_bonus_models = [];              /*回饋模組(以id為key)*/
    public $arr_product_cate = [];             /*商品類型(以id為key)*/
    public $arr_use_ad = [];                   /*套用廣告(以id為key)*/
    public $arr_vip_types = [];                 /*會員級別(以id為key)*/
    public $arr_partner_levels = [];            /*合夥人等級(以id為key)*/
    public $arr_center_levels = [];             /*中心等級(以id為key)*/
    public static $limit_type_to_definition = [ /*圓滿點數限制類型對應定義*/
        '1' => ['increasing_limit_invest', '功德圓滿點數'],
        '3' => ['increasing_limit_other', '其他圓滿點數'],
        '2' => ['increasing_limit_consumption', '消費圓滿點數'],
    ];

    public function __construct($now_time = null)
    {
        $this->MemberInstance = new MemberInstance(0);
        $this->PointRecords = new PointRecords(0);

        if (!$now_time) {
            $now_time = time();
        }
        $this->now_time = $now_time;
        $this->now_time_f = date('Y-m-d H:i:s', $this->now_time);

        /*取得基本計算設定*/
        $this->bonus_setting = BonusSettingHelper::get_bonus_setting(); //取得回饋設定資料 table: bonus_setting
        $this->arr_bonus_models = BonusSettingHelper::get_bonus_models([], true)['db_data']; //取得模組資料 table: bonus_model
        $this->arr_product_cate = BonusSettingHelper::get_product_cate([], true)['db_data']; //取得商品類型資料 1＝>投資, 2=>消費
        $this->arr_use_ad = BonusSettingHelper::get_use_ad([], true)['db_data']; //取得套用廣告資料 0=>否, 1=>是
        $this->arr_vip_types = MemberInstance::get_vip_types([], true)['db_data']; //取得會員級別資料 table: vip_type
        $this->arr_partner_levels = MemberInstance::get_partner_levels([], true)['db_data']; //取得合夥人等級資料 table: partner_level
        $this->arr_center_levels = MemberInstance::get_center_levels([], true)['db_data']; //取得中心等級資料 table: center_level
        // dump($this->bonus_setting);

        /*組織相關回饋人員(系統)*/

        /*系統帳戶id*/
        $this->init_user_set(config('extra.skychakra.member_system'));

        /*月分紅帳戶id*/
        $this->init_user_set(config('extra.skychakra.member_month_divided'));

        /*組織相關回饋人員(持有增值積分者，用於檢查「積分自動轉換」)*/
        $this->get_accounts_has_pi();
    }

    /**
     * getter
     */
    public function get($key)
    {
        return $this->$key;
    }

    /**
     * 初始化會員統計項目
     */
    public function init_user_set($user_id, $local_user_data = null)
    {
        if (!isset($this->user_cal[$user_id])) { /*未建立過會員*/
            /*初始化統計項目*/
            $this->user_cal[$user_id] = json_decode(json_encode(self::$user_set), true);
            /*設定基本資料*/
            if ($local_user_data) {
                $user_data = $local_user_data; /*有傳入的資料*/
            } else {
                $this->MemberInstance->change_user_id($user_id);
                $user_data = $this->MemberInstance->get_user_data(); /*資料庫撈取*/
            }
            $this->set_user_column_data($user_id, $user_data);
        } else if ($local_user_data) { /*由提供會員資料就只更新會員資料，避免影響到統計項目*/
            $this->set_user_column_data($user_id, $local_user_data);
        }
        if (!isset($this->final_user_status[$user_id])) { /*未建立過會員*/
            $this->final_user_status[$user_id] = [ /*初始化最終應調整的會員狀態*/
                'vip_type' => $this->user_cal[$user_id]['data']['vip_type'],
                'vip_type_course' => $this->user_cal[$user_id]['data']['vip_type_course'],
                'partner_level_id' => $this->user_cal[$user_id]['data']['partner_level_id'],
                'partner_accumulation' => $this->user_cal[$user_id]['data']['partner_accumulation'],
                'increasing_limit_invest' => $this->user_cal[$user_id]['data']['increasing_limit_invest'],
                'increasing_limit_consumption' => $this->user_cal[$user_id]['data']['increasing_limit_consumption'],
                'increasing_limit_other' => $this->user_cal[$user_id]['data']['increasing_limit_other'],
                'point_increasable' => $this->user_cal[$user_id]['data']['point_increasable'],
                'point' => $this->user_cal[$user_id]['data']['point'],
            ];
        }
    }

    /*擷取資料中特定欄位設定為會員基本資料*/
    private function set_user_column_data($user_id, $user_data)
    {
        $this->user_cal[$user_id]['data'] = [
            'id' => $user_data['id'] ?? 0,
            'upline_user' => $user_data['upline_user'] ?? 0,
            'registration_from' => $user_data['registration_from'] ?? 0,
            'vip_type' => $user_data['vip_type'] ?? 0,
            'vip_type_course' => $user_data['vip_type_course'] ?? 0,
            'center_level_id' => $user_data['center_level_id'] ?? 0,
            'center_raiser_id' => $user_data['center_raiser_id'] ?? 0,
            'partner_level_id' => $user_data['partner_level_id'] ?? 0,
            'partner_accumulation' => $user_data['partner_accumulation'] ?? 0,
            'increasing_limit_invest' => $user_data['increasing_limit_invest'] ?? 0,
            'increasing_limit_consumption' => $user_data['increasing_limit_consumption'] ?? 0,
            'increasing_limit_other' => $user_data['increasing_limit_other'] ?? 0,
            'point_increasable' => $user_data['point_increasable'] ?? 0,
            'point' => $user_data['point'] ?? 0,
            'auto_partner' => $user_data['auto_partner'] ?? 0,
            'supplier_bonus' => $user_data['supplier_bonus'] ?? 0,
        ];
    }

    /**
     * 設定購買者
     */
    public function set_buyer_id(int $buyer_id)
    {
        $this->buyer_id = $buyer_id;
    }

    /**
     * 設定購買者的推薦者
     */
    public function set_buyer_topline_id(int $buyer_topline_id)
    {
        $this->buyer_topline_id = $buyer_topline_id;
    }

    /**
     * 設定訂單編號
     */
    public function set_orderform_id(int $orderform_id)
    {
        $this->orderform_id = $orderform_id;
    }

    /**
     * 設定訂單編號
     */
    public function set_order_number(string $order_number)
    {
        $this->order_number = $order_number;
    }

    /**
     * 設定最終應處理的「會員級別」or「課程進度」，並同步更新會員最終狀態
     */
    public function set_final_vip_type(int $user_id, int $new_vip_type, int $check_type)
    {
        if ($check_type == 1) { /*設定「會員級別」*/
            $check_column = 'vip_type';
        } else if ($check_type == 2) { /*設定「課程進度」*/
            $check_column = 'vip_type_course';
        }
        /*已記錄要調整的會員級別*/
        $rec_vip_type = $this->final_user_status[$user_id][$check_column];

        $type_key_rank = array_keys($this->arr_vip_types);
        $rec_rank = array_search($rec_vip_type, $type_key_rank);
        $rec_rank = $rec_rank !== false ? $rec_rank : -1;
        $new_rank = array_search($new_vip_type, $type_key_rank);
        $new_rank = $new_rank !== false ? $new_rank : -1;

        if ($new_rank < $rec_rank) { /*檢查新調整的級別 是否低級於 已記錄要調整的級別 (新級別不得低於舊級別)*/
            return; /*不記錄要調整到此新會員級別*/
        }

        /*更新要調整的級別*/
        $this->final_user_status[$user_id][$check_column] = $new_vip_type;
    }

    /**
     * 依投入商品 回傳「校正CV金額」
     */
    public function get_count_cv($product)
    {
        /*計算「校正CV金額」*/
        //商品CV - 折抵的功德圓滿點數 - 折抵的消費圓滿點數
        $count_cv = $product['price_cv'] - $product['deduct_invest'] - $product['deduct_consumption'];
        return (float)$count_cv;
    }

    /**
     * 依投入「校正CV金額」累計積分資金池增加量，並回傳「分享CV金額」(30,70%)
     */
    public function count_share_cv(float $count_cv)
    {
        /*計算「分享CV金額」*/
        $share_cv = $count_cv * $this->bonus_setting['cv_rate'];
        return  $share_cv;
    }

    /**
     * 依商品跟會員id 回傳「會員級別燒傷」後應計算回饋的「分享CV金額」
     */
    public function count_share_cv_vip_type($product, int $target_id)
    {
        $vip_type = $this->user_cal[$target_id]['data']['vip_type'] ?? 0; //取得vip級別
        $vip_type = $this->arr_vip_types[$vip_type] ?? [];
        $burn_cv = $vip_type['burn_cv'] ?? 0;

        /*若「商品CV金額」若高於「燒傷CV金額」，則依「燒傷CV金額」計算回饋*/
        if ($product['price_cv'] >= $burn_cv) {
            $product['price_cv'] = $burn_cv;
        }

        /*計算「校正CV金額」*/
        $count_cv = $this->get_count_cv($product);

        /*計算「分享CV金額」*/
        $share_cv = $this->count_share_cv($count_cv);

        return $share_cv;
    }

    /**
     * 依投入回饋模駔id、計算類型、分享CV金額 回傳「應分潤金額」
     */
    public function count_available_cv(int $bonus_model_id, int $count_type, float $share_cv)
    {
        $bonus_model = $this->arr_bonus_models[$bonus_model_id] ?? [];
        // dump($bonus_model);

        /*計算「應分潤趴數」*/
        $partner_mode_check = false; /*預設採用「一般回饋」; 如果是true ==「合夥批發回饋」*/
        $use_partner_mode = $bonus_model['use_partner_mode'] ?? 0;
        if ($use_partner_mode) { /*有使用「合夥批發回饋」*/
            /*購買者的推薦者具有效合夥人身分:有合夥等級 且 (有功德圓滿點數或啟用自動升級)*/
            if (($this->user_cal[$this->buyer_topline_id]['data']['partner_level_id'] ?? 0) > 0 && //至少是微合夥人
                (
                    ($this->user_cal[$this->buyer_topline_id]['data']['increasing_limit_invest'] ?? 0) > 0 || //功德圓滿點數>0
                    ($this->user_cal[$this->buyer_topline_id]['data']['auto_partner'] ?? 0) == 2 //自動升級合夥人等級
                )
            ) {
                /* 購買者自身「會員級別」為【任督級別】以下*/
                $type_key_rank = array_keys($this->arr_vip_types);
                $check_rank = array_search(config('extra.skychakra.check_buyer_partner_bonus_type_id'), $type_key_rank); //「合夥批發回饋」要求購買者「會員級別」低於任督級別
                $check_rank = $check_rank !== false ? $check_rank : -1;
                $cur_rank = array_search($this->user_cal[$this->buyer_id]['data']['vip_type'], $type_key_rank);
                $cur_rank = $cur_rank !== false ? $cur_rank : -1;
                if ($cur_rank < $check_rank) {
                    $partner_mode_check = true;
                }
            }
        }

        if ($partner_mode_check) {/*採用「合夥批發回饋」*/
            $count_type_column = match ($count_type) {
                1 => 'partner_recommend',                // 推廣獎勵(合夥)
                2 => 'partner_partner',                  // 合夥平級獎勵(合夥)
                3 => 'partner_marketing_dept',            // 行政/廣告部門(合夥)
                4 => 'partner_sales_dept',                // 業務部門(合夥)
                5 => 'partner_executive_director',        // 大總監(合夥)
                6 => 'partner_center_director',           // 中心總監(合夥)
                7 => 'partner_center_founder',            // 中心發起人(合夥)
                8 => 'partner_lecturer',                 // 講師獎勵(合夥)
                9 => 'partner_center',                   // 中心獎勵(合夥)
                10 => 'partner_dividend_month',           // 月分紅(合夥)
                11 => 'partner_center_divided_to_raiser', // 中心獎勵-發起者佔比(合夥)
                default => '',
            };
        } else { /*採用「一般回饋」*/
            $count_type_column = match ($count_type) {
                1 => 'normal_recommend',                // 推廣獎勵
                2 => 'normal_partner',                  // 合夥平級獎勵
                3 => 'normal_marketing_dept',            // 行政/廣告部門
                4 => 'normal_sales_dept',                // 業務部門
                5 => 'normal_executive_director',        // 大總監
                6 => 'normal_center_director',           // 中心總監
                7 => 'normal_center_founder',            // 中心發起人
                8 => 'normal_lecturer',                 // 講師獎勵
                9 => 'normal_center',                   // 中心獎勵
                10 => 'normal_dividend_month',           // 月分紅
                11 => 'normal_center_divided_to_raiser', // 中心獎勵-發起者佔比
                default => '',
            };
        }
        $ratio = $this->arr_bonus_models[$bonus_model_id][$count_type_column] ?? 0; //取得分潤趴數
        $ratio = (float)$ratio / 100;
        return $share_cv * $ratio;
    }

    /**
     * 依投入回饋模駔id、分享CV金額 回傳「應分潤金額」
     */
    public function count_ad_available_cv(int $bonus_model_id, float $share_cv)
    {
        $ratio = $this->arr_bonus_models[$bonus_model_id]['ad_bonus'] ?? 0;
        $ratio = (float)$ratio / 100;
        return (float)$share_cv * $ratio;
    }

    /**
     * 依投入金額累計資金池異動
     */
    public function add_pi_pool(float $price_num)
    {
        $this->pi_pool_change += $price_num;
    }

    /**
     * 依會員id與available_cv 累計「增值積分價值異動」與「本次累積應分潤金額」
     */
    public function add_available_cv(int $target_id, float $available_cv)
    {
        $this->pi_change += $available_cv; //本次累積應分潤金額
        $this->user_cal[$target_id]['total_available_cv'] += $available_cv; //累計增值積分價值
        // dump($target_id.':'.$available_cv);
    }

    /**
     * 依組會員id、available_cv與回饋模組id 按照「發起者占比」為中心&發起者累計「本次累積應分潤金額」與「本次可取得其他圓滿點數」
     */
    public function add_available_cv_center(int $target_id, float $available_cv, int $bonus_model_id)
    {
        /*取得「發起者」的「應分潤金額」*/
        $available_cv_divided = $this->count_available_cv($bonus_model_id, 11, $available_cv);
        $center_raiser_id = $this->user_cal[$target_id]['data']['center_raiser_id'] ?? 0; //中心的發起者id
        $this->init_user_set($center_raiser_id); //初使化中心發起者
        $this->add_available_cv($center_raiser_id, $available_cv_divided);
        /*可額外獲得「其他圓滿點數」(2024-11-17 天脈決定不回饋「其他圓滿點數」)*/
        // $this->add_limit_other($center_raiser_id, $available_cv_divided);

        /*取得「中心」的「應分潤金額」*/
        $available_cv_last = $available_cv - $available_cv_divided; //先扣除發起者的那份
        $this->add_available_cv($target_id, $available_cv_last); //再把剩下的給中心
        /*可額外獲得「其他圓滿點數」(2024-11-17 天脈決定不回饋「其他圓滿點數」)*/
        // $this->add_limit_other($target_id, $available_cv_last);
    }

    /**
     * 新增營運獎勵
     */
    public function add_operation_cv(int $target_id, float $available_cv, int $bonus_model_id, string $bonus_type)
    {
        //取得應分潤%數

    }

    /**
     * 依會員id與投資金額 累計「本次累積投資金額」
     */
    public function add_total_invest(int $target_id, float $invest_num)
    {
        $this->user_cal[$target_id]['total_invest'] += $invest_num;
    }

    /**
     * 依會員id與share_cv 累計「本次變化消費圓滿點數」
     */
    public function add_limit_consumption(int $target_id, float $share_cv)
    {
        $add_num = $share_cv * $this->bonus_setting['limit_c_rate'];
        $this->user_cal[$target_id]['increasing_limit_consumption'] += $add_num;
    }

    /**
     * 依會員id與cv_num 累計「本次變化其他圓滿點數」
     */
    public function add_limit_other(int $target_id, float $cv_num)
    {
        $add_num = $cv_num * $this->bonus_setting['limit_o_rate'];
        $this->user_cal[$target_id]['increasing_limit_other'] += $add_num;
    }

    /**
     * 依商品累計「供應商回饋」
     * (純累積供應商金額，最後一次調整「增值積分資金池」異動CV金額、「增值積分」異動CV金額...)
     */
    public function add_supplier_bonus(array $product)
    {
        $distributor_id = $product['distributor_id'];
        if ($distributor_id) {
            //如果供應商不在，初使化供應商資料
            $this->init_user_set($distributor_id);

            /*若回饋方式為「增值積分」*/
            if ($product['supplier_bonus'] == 1) {
                $count_cv = $product['price_supplier'];
                /*供應商可獲得「其他圓滿點數」*/
                $this->add_limit_other($distributor_id, $count_cv);

                $this->add_pi_pool($count_cv);

                $share_cv = $this->count_share_cv($count_cv);
                $this->pi_change += $share_cv;
                /*累計供應商金額*/
                $this->user_cal[$distributor_id]['price_supplier'] += $share_cv;
            }
        }
    }


    /*依處理類型，及已於user_cal統計的結果 實際進行贈送並回傳發送前後的現值與人員變化量統計*/
    public function send_by_cal(string $main_msg, $auto_next = true)
    {
        // $check_user_id = 4;
        // dump($this->get_pi_value());
        // dump('-----------------');
        // dump($this->get('pi_pool_change'));
        // dump($this->get('pi_change'));
        // dump($this->get_pi_value_new());
        // dump('-----------------');

        /*對本次的購買者計算投資結果(可能異動資金池、分配積分金額)*/
        $this->arrange_investment($this->buyer_id);

        // 整理最終須回饋紀錄(包含「積分自動轉換」的調整)
        $this->arrange_final_data();

        // dump($this->get('pi_pool_change'));
        // dump($this->get('pi_change'));
        // dump($this->get_pi_value_new());
        // dump($this->get('user_cal')[$check_user_id]);
        // dump($this->get('final_user_status')[$check_user_id]);
        // dump(array_filter($this->get('final_points_record'), function($gg)use($check_user_id){ return $gg['user_id']==$check_user_id; }));
        // dump(array_filter($this->get('final_points_increasing_record'), function($gg)use($check_user_id){ return $gg['user_id']==$check_user_id; }));
        // dump(array_filter($this->get('final_increasing_limit_record'), function($gg)use($check_user_id){ return $gg['user_id']==$check_user_id; }));
        // dump($this->auto_invest);
        // exit;

        /*實際做出資料異動，並取得本次需自動投資的紀錄*/
        $this->send_final_data($main_msg);

        if ($auto_next) {
            $auto_invest = $this->auto_invest;
            while (count($auto_invest) > 0) {
                $next_invest = collect([]); /*需再次自動投資紀錄(預設無)*/
                /*依自動投資紀錄逐個會員處理自動投資*/
                foreach ($auto_invest as $user_id => $invest_num) {
                    /*建立一個新的積分計算，並附上投資者、投資金額*/
                    $BonusHelperAuto = new self($this->now_time);
                    $BonusHelperAuto->add_total_invest($user_id, $invest_num);
                    $BonusHelperAuto->init_user_set($user_id);
                    $BonusHelperAuto->set_buyer_id($user_id);
                    $upline_user = $BonusHelperAuto->get('user_cal')[$user_id]['data']['upline_user'] ?? 0;
                    $BonusHelperAuto->init_user_set($upline_user);
                    $BonusHelperAuto->set_buyer_topline_id($upline_user);
                    /*依統計結果處理回饋(不直接進行自動投資)*/
                    $sub_invest = $BonusHelperAuto->send_by_cal('自動升級', false);
                    /*將 需再次自動投資紀錄 與 自動升級產生的自動投資紀錄 合併，把相同會員id的投資金額加總*/
                    $sub_invest = collect($sub_invest);
                    $next_invest = $next_invest->union($sub_invest)->map(function ($value, $key) use ($next_invest, $sub_invest) {
                        $sum = 0;
                        if ($next_invest->has($key)) {
                            $sum += $next_invest[$key];
                        }
                        if ($sub_invest->has($key)) {
                            $sum += $sub_invest[$key];
                        }
                        return $sum;
                    });
                }
                /*把 需再次自動投資紀錄 賦值給next_invest，供while判斷是否再次觸發自動投資*/
                $auto_invest = $next_invest->all();
            }
        }
        return $this->auto_invest;
    }

    /**
     * 依 user_cal統計的結果 整理最終須回饋紀錄(包含計算積分增值量&「積分自動轉換」的調整)
     */
    private function arrange_final_data()
    {
        $pi_value_ori = $this->get_pi_value();      /*增值積分現值*/
        $pi_value_new = $this->get_pi_value_new();  /*增值積分調整後現值*/

        /*依累積計算:投資回饋*/
        foreach ($this->user_cal as $user_id => $user_set) {
            /*依累積計算:消費圓滿點數*/
            $increasing_limit_consumption = $this->user_cal[$user_id]['increasing_limit_consumption'];
            if ($increasing_limit_consumption > 0) {
                $this->set_final_increasing_limit_record($user_id, '線上消費回饋消費圓滿點數', $increasing_limit_consumption, 2, 1);
            }
            /*依累積計算:其他圓滿點數*/
            $increasing_limit_other = $this->user_cal[$user_id]['increasing_limit_other'];
            if ($increasing_limit_other > 0) {
                $this->set_final_increasing_limit_record($user_id, '線上消費回饋其他圓滿點數', $increasing_limit_other, 3, 1);
            }
            /*依累積計算:增值積分*/
            $total_available_cv = $this->user_cal[$user_id]['total_available_cv'];
            if ($total_available_cv > 0) {
                $point_increasable = (float)($total_available_cv / $pi_value_ori);
                $this->set_final_points_increasing_record($user_id, '線上消費回饋增值積分', $point_increasable);
                /*計算應檢查增值的金額(新取得積分的價值，要考慮增值)*/
                $this->user_cal[$user_id]['check_num'] += $point_increasable * $pi_value_new;
            }
            /*依累積計算:供應商回饋(增值積分)*/
            $price_supplier = $this->user_cal[$user_id]['price_supplier'];
            if ($price_supplier > 0) {
                $point_increasable = (float)($price_supplier / $pi_value_ori);
                $this->set_final_points_increasing_record($user_id, '線上消費供應商回饋', $point_increasable);
                /*計算應檢查增值的金額(新取得積分的價值，要考慮增值)*/
                $this->user_cal[$user_id]['check_num'] += $point_increasable * $pi_value_new;
            }
            /*計算應檢查增值的金額(過去積分的價值差異)*/
            $old_point_increasable = $this->user_cal[$user_id]['data']['point_increasable'];
            $this->user_cal[$user_id]['check_num'] += ($pi_value_new - $pi_value_ori) * $old_point_increasable;

            /*處理「積分自動轉換」*/
            $this->check_point_limit($user_id);
        }
    }

    /**
     * 依累計投資金額計算可獲得「功德圓滿點數」&計算投資回饋各會員的「應分潤金額」，並同步更新會員最終狀態
     */
    private function arrange_investment($levelup_user_id)
    {
        $this->MemberInstance->change_user_id($levelup_user_id);
        $total_invest = $this->user_cal[$levelup_user_id]['total_invest'] ?? 0; //取得本次累計投資金額
        if ($total_invest > 0) { /*本次有累計投資金額(不允許負，因為關係到晉升)*/

            /*找出本次投資後可晉升到哪個階級*/
            $total_partner_invest = $this->final_user_status[$levelup_user_id]['partner_accumulation'] ?? 0; /*目前累積投資金額*/
            $total_partner_invest += $total_invest; /*加上本次投資金額*/
            $this->final_user_status[$levelup_user_id]['partner_accumulation'] = $total_partner_invest; /*更新目前累積投資金額*/
            $new_partner_level = $this->get_new_partner_level($total_partner_invest);
            // dump($new_partner_level);exit;

            /*依新階級倍率*本次投資金額 回饋「功德圓滿點數」*/
            $increasing_limit_invest = $new_partner_level['ratio'] * $total_invest;

            /*為投資者添加「功德圓滿點數」*/
            $this->set_final_increasing_limit_record($levelup_user_id, '線上消費回饋功德圓滿點數', $increasing_limit_invest, 1, 1);

            /*依 new_partner_level 取得的等級檢查結果，並添加等級關係紀錄*/
            $pre_level_id = $this->final_user_status[$levelup_user_id]['partner_level_id'];
            $check_result = $this->MemberInstance->check_partner_level($pre_level_id, $new_partner_level['id']);
            if ($check_result['msg'] == '') { /*無錯誤訊息*/
                $this->final_user_status[$levelup_user_id]['partner_level_id'] = $check_result['new_level_id'];
                //POINT: 更動到DB
                $this->MemberInstance->create_partner_level_relation($check_result['pre_rank'], $check_result['new_rank']);
            }
            $pre_rank = $check_result['pre_rank'];
            $new_rank = $check_result['new_rank'];

            /*「投資回饋」，依各階級計算可回饋金額*/
            $type_key_rank = array_keys($this->arr_partner_levels);
            foreach ($type_key_rank as $rank => $level_id) {
                if ($rank <= $pre_rank) {
                    continue;
                } /*等級小等於過去階級*/
                if ($rank > $new_rank) {
                    break;
                }     /*等級大於新的階級*/

                /*計算階級貢獻差異*/
                $level_contribution_diff = $this->arr_partner_levels[$level_id]['contribution'];
                if ($rank > 0) {
                    $level_contribution_diff -= $this->arr_partner_levels[$type_key_rank[$rank - 1]]['contribution'];
                }
                // dump($level_contribution_diff);

                $registration_from = $this->user_cal[$levelup_user_id]['data']['registration_from'] ?? 1;
                //TODO: 推3返本回饋在這
                if ($registration_from == 1) {/*「直推」，進行「推3返本回饋」*/
                    /*組織相關回饋人員*/
                    /*升級者*/
                    $this->init_user_set($levelup_user_id);
                    /*升級者的推薦者*/
                    $upline_user = $this->user_cal[$levelup_user_id]['data']['upline_user'] ?? 0;
                    $this->init_user_set($upline_user);

                    /*計算是第幾個*/
                    $nth_data = $this->get_nth_data_by_partner_level($levelup_user_id, $level_id);

                    /*設定贈送訊息*/
                    // 無用變數@@
                    // $msg = '第' . $nth_data['level_count'] . '個' . $this->arr_partner_levels[$level_id]['name'] . '回饋(' . ($nth_data['nth_ratio'] * 100) . '%)';

                    /*計算可贈送「增值積分」*/
                    $upline_user_partner_level_id = $this->user_cal[$upline_user]['data']['partner_level_id'] ?? 0;
                    $check_result = $this->MemberInstance->check_partner_level($level_id, $upline_user_partner_level_id);
                    if ($check_result['msg']) { /*回饋等級高於自己的等級*/
                        /*改以自己等級的貢獻金額做為級差*/
                        $upline_user_partner_level = $this->arr_partner_levels[$upline_user_partner_level_id] ?? [];
                        $count_level_contribution_diff = $upline_user_partner_level['contribution'] ?? 0;
                    } else {
                        $count_level_contribution_diff = $level_contribution_diff;
                    }
                    $send_num = (float)($count_level_contribution_diff * $nth_data['nth_ratio']);
                    $this->add_pi_pool($send_num);
                    $this->add_available_cv($upline_user, $this->count_share_cv($send_num));
                } else { /*「廣告」，進行「總部投資回饋」*/
                    /*組織相關回饋人員*/
                    /*排除購買者後取得有效合夥人加權結果(內會組織有效合夥人)*/
                    $weight_result = $this->get_active_partner_weight_result($levelup_user_id, $rank);
                    // dump($weight_result);

                    /*設定贈送訊息*/
                    // 無用變數@@
                    // $msg = '廣告' . $this->arr_partner_levels[$level_id]['name'] . '回饋(' . ($this->bonus_setting['ad_partner_rate'] * 100) . '%)';

                    /*計算可贈送「增值積分」*/
                    $send_num = $level_contribution_diff * $this->bonus_setting['ad_partner_rate'];
                    /*計算各有效合夥人可分金額*/
                    $total_weight = $weight_result['total_weight'];
                    $active_partner = $weight_result['user_weight'];
                    foreach ($active_partner as $partner_id => $weight) {
                        $divided_num = (float)($send_num * $weight / $total_weight);
                        $this->add_pi_pool($divided_num);
                        $this->add_available_cv($partner_id, $this->count_share_cv($divided_num));
                    }
                }
            }
        }
    }

    /**
     * 依總累積投資金額 回傳對應合夥人等級id
     */
    public function get_new_partner_level(float $total_contribution)
    {
        $types = array_values($this->arr_partner_levels);
        $last_index = count($types) - 1;
        /*預設回傳最低等的倍率*/
        $new_type = json_decode(json_encode($types[0]), true);
        $new_type["id"] = 0;                  /*修正預設等級id(無)*/
        $new_type["name"] = '';               /*修正預設名稱(無)*/
        $new_type["orderform_ad_weight"] = 0; /*修正預設權重(無)*/
        // dd($new_type);
        foreach ($types as $key => $value) {
            if ($total_contribution >= (int)$value['contribution']) { // 如果累積投資金額比條件金額大
                if ($key == $last_index) { // 此階層已是最後一階層
                    $new_type = $value; // 當前等級就是所屬等級
                    break;
                } else {
                    continue; // 到下一等級比對
                }
            } else if ($key != 0) { // 如果累積投資金額比條件金額小 且 不是第一個階層
                $new_type = $types[$key - 1]; // 當前等級的前一等就是所屬等級
                break;
            }
        }

        return $new_type;
    }

    /*依合夥等級回傳下一階層合夥等級(無下一等級則回傳[])*/
    public function get_next_partner_level(int $ori_partner_level_id)
    {
        $type_key_rank = array_keys($this->arr_partner_levels);
        $cur_rank = array_search($ori_partner_level_id, $type_key_rank);
        $cur_rank = $cur_rank !== false ? $cur_rank : -1;
        if ($cur_rank == count($type_key_rank) - 1) {
            $partner_level_next = [];
        } else {
            $partner_level_next = $this->arr_partner_levels[$type_key_rank[$cur_rank + 1]];
        }
        return $partner_level_next;
    }

    /**
     * 依傳入會員ID 處理增值調整(扣圓滿點數、自動投資、拋轉積分)，並同步更新會員最終狀態
     */
    private function check_point_limit(int $target_id)
    {
        if (in_array($target_id, [0, config('extra.skychakra.member_system'), config('extra.skychakra.member_month_divided')])) {
            /*無帳號、系統帳號、月分紅帳號免檢查自動轉換*/
            return;
        }
        $pi_value_ori = $this->get_pi_value();                        /*增值積分現值*/
        $pi_value_new = $this->get_pi_value_new();                    /*增值積分調整後現值*/
        $pi_value_change = $this->user_cal[$target_id]['check_num'];  /*需檢查增值的量*/
        if ($pi_value_change <= 0) {
            return;
        } /*已無待檢查價值變化量*/

        $levelup_result = $this->auto_partner_levelup($target_id, $pi_value_change);
        if ($levelup_result) {
            return; /*有做處理了，不再進行額外檢查*/
        }

        /*逐個圓滿點數檢查增值量*/
        foreach (self::$limit_type_to_definition as $limit_type => $definition) {
            if ($pi_value_change <= 0) {
                return;
            } /*已無待檢查價值變化量*/

            $limit_column = $definition[0];                                     /*對應圓滿點數欄位*/
            $limit_name = $definition[1];                                       /*對應圓滿點數中文名稱*/
            $user_limit = $this->final_user_status[$target_id][$limit_column];  /*對應圓滿點數持有數量*/
            if ($user_limit > 0) { /*有圓滿點數*/
                // dump($limit_name.':'.$user_limit);
                /*扣除指定圓滿點數(扣除量最多為持有的圓滿點數量)*/
                $limit_change_num = $pi_value_change >= $user_limit ? $user_limit : $pi_value_change;
                $limit_change_num = (float)(-1.0 * $limit_change_num);
                $this->set_final_increasing_limit_record($target_id, '積分增值折抵' . $limit_name, $limit_change_num, $limit_type, 2);

                if ($pi_value_change >= $user_limit) { /*如果增值量大於等於持有圓滿點數(達圓滿)*/
                    /*原持有增值積分價值 = 最終持有增值積分價值 - 本次額外獲得的價值*/
                    $point_increasable_ori_value = $this->final_user_status[$target_id]['point_increasable'] * $pi_value_new - $pi_value_change;
                    /*達圓滿金額 = 原持有增值積分價值 + 剩餘功德圓滿點數*/
                    $reach_num = $point_increasable_ori_value + $user_limit;
                    if ($reach_num > 0) {
                        /*只要圓滿就先拋轉積分*/
                        $num = $reach_num / $pi_value_new;
                        $this->point_increasable_to_point($target_id, $num, $limit_name . '達可增值上限拋轉現金積分');
                    }
                }
                $pi_value_change += $limit_change_num; /*調整需檢查增值的量(用於後續檢查)*/
            }
        }

        /*圓滿點數都檢查完*/
        if ($pi_value_change <= 0) {
            return;
        } /*已無待檢查價值變化量*/

        /*計算全部增值積分價值*/
        $point_increasable = $this->final_user_status[$target_id]['point_increasable'];
        if ($point_increasable > 0) {
            /*拋轉現金積分*/
            $this->point_increasable_to_point($target_id, $point_increasable, '達可增值上限拋轉現金積分');
        }
    }

    /*依傳入會員ID、投資金額、觸發自動升級時剩餘的圓滿點數量 處理自動升級(會回傳是否有做處理)*/
    private function auto_partner_levelup(int $levelup_user_id, float $pi_value_change)
    {
        /*如果會員本身有設定自動升級，且會員本身有合夥等級*/
        if (
            $this->user_cal[$levelup_user_id]['data']['auto_partner'] == 2 &&
            $this->user_cal[$levelup_user_id]['data']['partner_level_id'] > 0
        ) {
            $pi_value_ori = $this->get_pi_value();      /*增值積分現值*/
            $pi_value_new = $this->get_pi_value_new();  /*增值積分調整後現值*/

            $increasing_limit_invest = $this->final_user_status[$levelup_user_id]['increasing_limit_invest']; /*最終持有功德圓滿點數量*/
            $partner_accumulation = $this->final_user_status[$levelup_user_id]['partner_accumulation'] ?? 0;  /*最終累積投資金額*/
            $partner_level_id = $this->final_user_status[$levelup_user_id]['partner_level_id'] ?? 0;          /*最終調整合夥等級*/
            $partner_level_next = $this->get_next_partner_level($partner_level_id);
            if ($partner_level_next) { /*有下一階級*/
                /*計算超過功德圓滿點數的增值量(當功德圓滿小於等於0則視為全部超過)*/
                $exeed_limit = $increasing_limit_invest > 0 ? $pi_value_change - $increasing_limit_invest : $pi_value_change;
                if ($exeed_limit >= 0) { /*有超過*/
                    if ($increasing_limit_invest > 0) { /*原本持有功德圓滿點數*/
                        /*扣除持有的圓滿點數*/
                        $this->set_final_increasing_limit_record($levelup_user_id, '積分增值折抵功德圓滿點數', (-1.0 * $increasing_limit_invest), 1, 2);
                    }
                    /*計算升級差額*/
                    $diff_contribution = $partner_level_next['contribution'] - $partner_accumulation;
                    /*計算最終持有圓滿點數價值是否足夠升級*/
                    $all_num = $this->final_user_status[$levelup_user_id]['point_increasable'] * $pi_value_new;
                    if ($all_num > $diff_contribution && $diff_contribution > 0) { /*足夠升級 且 投資金額大於0*/
                        if ($exeed_limit > 0) {
                            $this->set_final_increasing_limit_record($levelup_user_id, '積分增值折抵功德圓滿點數', (-1.0 * $exeed_limit), 1, 1);
                        }

                        /*累計自動投資紀錄的金額*/
                        if (!($this->auto_invest[$levelup_user_id] ?? 0)) {
                            $this->auto_invest[$levelup_user_id] = 0;
                        }
                        $this->auto_invest[$levelup_user_id] += $diff_contribution;

                        /*扣除升級者用於投資的增值積分*/
                        $point_increasable = (float)(-1.0 * $diff_contribution / $pi_value_new);
                        $this->set_final_points_increasing_record($levelup_user_id, '自動升級合夥人等級', $point_increasable);

                        /*因應使用增值積分，總計「增值積分資金池」異動CV金額扣除*/
                        $this->add_pi_pool(-1.0 * $diff_contribution);
                        /*因應使用增值積分，總計「增值積分」異動CV金額扣除*/
                        $this->pi_change += $point_increasable * $pi_value_ori;
                    } else {
                        if ($exeed_limit > 0) {
                            $this->set_final_increasing_limit_record($levelup_user_id, '不足自動升級預扣功德圓滿點數', (-1.0 * $exeed_limit), 1, 1);
                        }
                    }
                } else { /*沒超過*/
                    /*扣除增值量的圓滿點數*/
                    $this->set_final_increasing_limit_record($levelup_user_id, '積分增值折抵功德圓滿點數', (-1.0 * $pi_value_change), 1, 2);
                }
                return true; /*有做處理*/
            }
        }
        return false; /*沒有做處理*/
    }
    /*依會員id、消耗「增值積分」數量 處理拋轉「現金積分」*/
    private function point_increasable_to_point(int $target_id, float $point_increasable, string $transfer_msg)
    {
        $pi_value_ori = $this->get_pi_value();      /*增值積分現值*/
        $pi_value_new = $this->get_pi_value_new();  /*增值積分調整後現值*/
        /*設定添加「現金積分」*/
        $point_value = $point_increasable * $pi_value_new;
        $this->set_final_points_record($target_id, $transfer_msg, $point_value);
        /*計算需消耗的增值積分數量*/
        /*設定減少「增值積分」*/
        $this->set_final_points_increasing_record($target_id, $transfer_msg, (-1.0 * $point_increasable));
        $this->add_pi_pool(-1.0 * $point_value);                /*因增值積分減少，「增值積分資金池」異動CV金額需減少*/
        $this->pi_change -= $point_increasable * $pi_value_ori; /*因增值積分減少，「增值積分」異動CV金額需減少*/
    }

    /*設定最終應處理的「現金積分」紀錄，並同步更新會員最終狀態*/
    private function set_final_points_record($target_id, $msg, $change_num)
    {
        array_push($this->final_points_record, [
            'user_id' => $target_id,
            'msg' => $msg,
            'points' => $change_num,
            'belongs_time' => $this->now_time,
            'msg_time' => $this->now_time_f,
        ]);
        $this->final_user_status[$target_id]['point'] += $change_num;
    }
    /*設定最終應處理的「增值積分」紀錄，並同步更新會員最終狀態*/
    private function set_final_points_increasing_record($target_id, $msg, $change_num)
    {
        array_push($this->final_points_increasing_record, [
            'user_id' => $target_id,
            'msg' => $msg,
            'num' => $change_num,
            'create_time' => $this->now_time,
        ]);
        $this->final_user_status[$target_id]['point_increasable'] += $change_num;
    }

    /**
     * 添加最終應處理的「圓滿點數」紀錄，並同步更新會員最終狀態
     */
    private function set_final_increasing_limit_record($target_id, $msg, $change_num, $limit_type, $type)
    {
        $column = self::$limit_type_to_definition[$limit_type][0] ?? '';
        if (!$column) {
            throw new \Exception(Lang::get('圓滿點數無此限制類型'));
        }
        array_push($this->final_increasing_limit_record, [
            'user_id' => $target_id,
            'msg' => $msg,
            'num' => $change_num,
            'limit_type' => $limit_type,
            'type' => $type,
            'create_time' => $this->now_time,
        ]);
        $this->final_user_status[$target_id][$column] += $change_num;
    }

    /**
     * 依 final_user_status統計的結果 實際做出資料異動
     * //POINT: 更動到DB
     */
    private function send_final_data($send_msg)
    {
        // 沒用到的變數@@
        // $pi_value_ori = $this->get_pi_value();      /*增值積分現值*/
        $pi_value_new = $this->get_pi_value_new();  /*增值積分調整後現值*/

        /*處理會員狀態*/
        foreach ($this->final_user_status as $user_id => $user_set) {
            $this->MemberInstance->change_user_id($user_id);

            /*調整「會員級別」*/
            $vip_type_reward = $user_set['vip_type'] ?? 0;
            if ($vip_type_reward) { /*需調整*/
                $this->MemberInstance->update_vip_type($vip_type_reward);
            }

            /*調整「課程進度」*/
            $vip_type_course_reward = $user_set['vip_type_course'] ?? 0;
            if ($vip_type_course_reward) { /*需調整*/
                $this->MemberInstance->update_user_data(['vip_type_course' => $vip_type_course_reward]);
            }

            /*找出本次投資後可晉升到哪個階級*/
            $partner_accumulation = $user_set['partner_accumulation'] ?? 0; /*最終累積投資金額*/
            $new_partner_level = $this->get_new_partner_level($partner_accumulation);
            // dump($new_partner_level);exit;

            /*更新合夥等級&累積投資金額*/
            $this->MemberInstance->update_user_data([
                'partner_level_id' => $new_partner_level['id'],
                'partner_accumulation' => (float)$partner_accumulation,
            ]);
        }

        /*處理「現金積分」*/
        foreach ($this->final_points_record as $insertData) {
            if ($insertData['user_id'] == 0) { /*無會員，系統回收*/
                $insertData['user_id'] = config('extra.skychakra.member_system');
            }
            if ($this->order_number) {
                $insertData['msg'] .= '(' . HelperService::hidestr($this->order_number, 10, -3) . ')';
            }
            $this->PointRecords->change_user_id($insertData['user_id']);
            $this->PointRecords->add_records($insertData);
        }

        /*處理「增值積分」*/
        foreach ($this->final_points_increasing_record as $insertData) {
            if ($insertData['user_id'] == 0) { /*無會員，系統回收*/
                $insertData['user_id'] = config('extra.skychakra.member_system');
            }
            if ($this->order_number) {
                $insertData['msg'] .= '(' . HelperService::hidestr($this->order_number, 10, -3) . ')';
            }
            $this->PointRecords->change_user_id($insertData['user_id']);
            $this->PointRecords->add_point_increasable_record($insertData);
            /*如果是月分紅帳戶*/
            if ($insertData['user_id'] == config('extra.skychakra.member_month_divided')) {
                /*拆分月分紅到個月份*/
                $this->set_point_increasable_record($insertData['num']);
            }
        }

        /*處理「圓滿點數」*/
        foreach ($this->final_increasing_limit_record as $insertData) {
            if ($insertData['user_id'] == 0) { /*無會員，系統回收*/
                $insertData['user_id'] = config('extra.skychakra.member_system');
            }
            if ($this->order_number) {
                $insertData['msg'] .= '(' . HelperService::hidestr($this->order_number, 10, -3) . ')';
            }
            $this->MemberInstance->change_user_id($insertData['user_id']);
            $this->MemberInstance->add_increasing_limit_record(
                (float)$insertData['num'],
                $insertData['msg'],
                $insertData['limit_type'],
                $insertData['type'],
                $insertData['create_time'],
            );
        }

        /*更新「增值積分現值」*/
        if ($this->order_number) {
            $send_msg .= '(' . HelperService::hidestr($this->order_number, 10, -3) . ')';
        }
        Db::connection('main_db')->table('point_increasable_pool')->insert([
            'orderform_id' => $this->orderform_id,
            'user_id' => $this->buyer_id,
            'num' => $this->pi_pool_change,
            'msg' => $send_msg,
            'datetime' => $this->now_time,
            'point_value' => $pi_value_new,
        ]);
        Db::connection('main_db')->table('bonus_setting')->where('id', 1)->update([
            'value' => $pi_value_new,
        ]);
    }

    /*依給定積分數量，拆分認列個月份可分配機分數(月分紅認列積分)*/
    private function set_point_increasable_record(float $divided_num_all)
    {
        $divided_times = $this->bonus_setting['divided_times'];
        $divided_num_used = 0;
        $insert_data = [];
        foreach ($divided_times as $key => $ratio) {
            if ($key == count($divided_times) - 1) {
                $month_num = $divided_num_all - $divided_num_used;
            } else {
                $ratio = (float)$ratio / 100;
                $month_num = $divided_num_all * $ratio;
                $month_num = floor($month_num * 100000000) / 100000000;
                $divided_num_used += $month_num;
            }
            $record_time =  strtotime(date('Y-m-d H:i') . '+' . $key . 'Month');
            if ($month_num == 0) {
                continue;
            }
            array_push($insert_data, [
                'orderform_id' => $this->orderform_id,
                'num' => (float)$month_num,
                'datetime' => $record_time,
            ]);
        }
        Db::connection('main_db')->table('dividend_month_record')->insert($insert_data);
    }

    /**
     * 依會員id計算「個人點數GV」，並回傳對應貢獻的會員級別
     */
    public function get_vipe_type_by_gv(int $target_id)
    {
        /*自己與推薦者的id*/
        $user_down = Db::connection('main_db')->table('account')->where('upline_user', $target_id)->get();
        $user_down = CommonService::objectToArray($user_down);
        $ids = array_map(function ($user) {
            return $user['id'];
        }, $user_down);
        array_push($ids, $target_id);
        // dump($ids);

        /*篩選相關會員的已回饋訂單*/
        $orders = Db::connection('main_db')->table('orderform')
            ->where('do_award_time', '!=', '')
            ->whereIn('user_id', $ids)
            ->get();

        $orders = CommonService::objectToArray($orders);
        $order_ids = array_map(function ($order) {
            return $order['id'];
        }, $orders);

        array_push($order_ids, $this->orderform_id); /*多加入此筆訂單(因已回饋並不包含目前正在處理的訂單)*/
        // dump($order_ids);

        /*篩選相關訂單的消費商品..*/
        $gv_value = Db::connection('main_db')->table('orderform_product')
            ->selectRaw('SUM(price_cv - deduct_invest - deduct_consumption) as gv_value') //商品cv - 功德圓滿折抵 - 消費圓滿折抵 = gv
            ->where('vip_type_require', '>', '0') //需求會員級別 > 0
            ->where('product_cate', '2') //是消費商品
            ->whereIn('orderform_id', $order_ids)
            ->value('gv_value') ?? 0;
        // dump($gv_value);

        /*依GV找出對應等級*/
        $types = array_values($this->arr_vip_types);
        $last_index = count($types) - 1;
        $new_type = [];
        foreach ($types as $key => $value) {
            if ($gv_value >= (int)$value['rule']) { // 如果累積「個人點數(GV)」比條件金額大
                if ($key == $last_index) { // 此階層已是最後一階層
                    $new_type = $value; // 當前等級就是所屬等級
                    break;
                } else {
                    continue; // 到下一等級比對
                }
            } else if ($key != 0) { // 如果累積「個人點數(GV)」比條件金額小 且 不是第一個階層
                $new_type = $types[$key - 1]; // 當前等級的前一等就是所屬等級
                break;
            }
        }
        return $new_type;
    }

    /**
     * 依據升級會員id與要檢查的等級 回傳累計第幾個會員與相應「第幾個會員比率」
     */
    public function get_nth_data_by_partner_level(int $levelup_user_id, int $level_id)
    {
        //依上線取得所有下線
        $upline_user = $this->user_cal[$levelup_user_id]['data']['upline_user'] ?? 0;
        $down_lines = DB::connection('main_db')->table('account')
            ->select('id')
            ->where('upline_user', $upline_user)
            ->where('registration_from', '1') //直推
            ->orderBy('id', 'desc')
            ->get();

        $down_lines = CommonService::objectToArray($down_lines);
        $my_users_not_ad = array_map(function ($item) {
            return $item['id'];
        }, $down_lines);

        // dump($my_users_not_ad);exit;
        $level_relation = DB::connection('main_db')->table('partner_level_relation')
            ->where('user_id', $levelup_user_id)
            ->where('level_id', $level_id)
            ->first();
        $level_relation = CommonService::objectToArray($level_relation);

        $level_count = DB::connection('main_db')->table('partner_level_relation')
            ->where('id', '<=', $level_relation['id'] ?? 0)
            ->whereIn('user_id', $my_users_not_ad)
            ->where('level_id', $level_id)
            ->groupBy('user_id') /*避免有人員的重複紀錄*/
            ->get()->count(); // 使用 get() 解決 count() 分組合併問題
        // dump($level_count);
        if ($level_count > 0) {
            $ratio_index = $level_count % count($this->bonus_setting['recommend_times']);
            $ratio_index = $ratio_index ? $ratio_index : count($this->bonus_setting['recommend_times']);
            $nth_ratio = $this->bonus_setting['recommend_times'][$ratio_index - 1];
        } else {
            $nth_ratio = 0;
        }

        $nth_ratio = (float)$nth_ratio / 100;

        return [
            'level_count' => $level_count,  /*累計第幾個*/
            'nth_ratio' => $nth_ratio,      /*第幾個會員比率*/
        ];
    }

    /**
     * 依下層中心等級與上層中心等級 回傳CV分潤比率(區分下、上)
     */
    public function count_center_level_diff(int $center_lower, int $center_upper)
    {
        // 使用 cv_ratio 來計算分潤比例，而不是原本的權重計算
        // [
        //     1 => ['name' => '區級', 'orders' => 1, 'cv_ratio' => 10.00],
        //     2 => ['name' => '市級', 'orders' => 2, 'cv_ratio' => 15.00],
        //     3 => ['name' => '省級', 'orders' => 3, 'cv_ratio' => 20.00],
        // ]

        $level_ratio = [];

        /*處理下層中心分潤比率*/
        if ($center_lower <= 0 || !isset($this->arr_center_levels[$center_lower])) {
            /*若沒有等級*/
            $level_ratio['center_lower'] = 0;
        } else {
            /*使用該等級的 cv_ratio*/
            $level_ratio['center_lower'] = (float)($this->arr_center_levels[$center_lower]['cv_ratio'] ?? 0);
        }

        /*處理上層中心分潤比率*/
        if ($center_upper <= 0 || !isset($this->arr_center_levels[$center_upper])) {
            /*若沒有等級*/
            $level_ratio['center_upper'] = 0;
        } else {
            $upper_cv_ratio = (float)($this->arr_center_levels[$center_upper]['cv_ratio'] ?? 0);
            $lower_cv_ratio = $level_ratio['center_lower'];

            /*上層中心只能分配比下層中心更高的比率差額*/
            if ($upper_cv_ratio <= $lower_cv_ratio) {
                /*若上層中心等級的cv_ratio小於等於下層中心等級*/
                $level_ratio['center_upper'] = 0;
            } else {
                /*分配差額比率*/
                $level_ratio['center_upper'] = $upper_cv_ratio - $lower_cv_ratio;
            }
        }

        /*計算總分潤比率*/
        $total_ratio = $level_ratio['center_lower'] + $level_ratio['center_upper'];

        return [
            'total_weight' => $total_ratio, // 改為總分潤比率
            'level_weight' => $level_ratio, // 改為分潤比率
        ];
    }

    /*取得「增值積分」現值*/
    public function get_pi_value()
    {
        return $this->bonus_setting['pi_value'];
    }
    /*取得「增值積分」現值(依照增值積分異動量取得「增值積分」調整後現值)*/
    public function get_pi_value_new()
    {
        /*「增值積分」現值*/
        $pi_value_ori = $this->get_pi_value();

        /*計算累積「增值積分」數量*/
        $pi_last = (float)Db::connection('main_db')->table('account')->sum('point_increasable');
        // dump($pi_last);

        /*計算累積「增值積分資金池」金額*/
        $point_increasable_pool = (float)Db::connection('main_db')->table('point_increasable_pool')->sum('num');
        // dump($point_increasable_pool);

        /*回饋後「增值積分資金池」金額*/
        $new_pool = $point_increasable_pool + (float)$this->pi_pool_change;

        /*回饋後「增值積分」數量*/
        $pi_num = $pi_last + (float)($this->pi_change / $pi_value_ori);
        if ($pi_num) {
            $pi_value_new = $new_pool / $pi_num;
        } else {
            $pi_value_new = $pi_value_ori;
        }
        $pi_value_new = round($pi_value_new, 10);

        return $pi_value_new;
    }

    /**
     * 組織有效合夥人並回傳有效合夥人的ids
     */
    public function init_user_active_partner(int $exclude_id, int $partner_level_rank = -1)
    {
        $type_key_rank = array_keys($this->arr_partner_levels);
        $partner_level_ids = [];
        if ($partner_level_rank == -1) {
            array_push($partner_level_ids, 0);
        }

        foreach ($type_key_rank as $rank => $level_id) {
            if ($rank >= $partner_level_rank) {
                array_push($partner_level_ids, $level_id);
            }
        }
        // dump($partner_level_ids);

        $active_partner_ids = [];

        /*撈取有合夥等級且有剩餘功德圓滿點數且排除指定會員*/
        $users = DB::connection('main_db')->table('account')
            ->where('partner_level_id', '>', 0) /*有合夥等級*/
            ->where(function ($query) {
                /*有功德圓滿點數 或 啟用自動升級*/
                $query->where('increasing_limit_invest', '>', 0)
                    ->orWhere('auto_partner', 2);
            })
            ->where('id', '!=', $exclude_id)
            ->whereIn('partner_level_id', $partner_level_ids)
            ->get();

        $users = CommonService::objectToArray($users);

        foreach ($users as $user) {
            $this->init_user_set($user['id'], $user);
            array_push($active_partner_ids, $user['id']);
        }

        return $active_partner_ids;
    }

    /**
     * 依排除會員id 回傳有效合夥人加權結果
     */
    public function get_active_partner_weight_result(int $exclude_id, int $partner_level_rank = -1)
    {
        /*組織有效合夥人*/
        $active_partner_ids = $this->init_user_active_partner($exclude_id, $partner_level_rank);

        /*處理會員加權*/
        $total_weight = 0;
        $user_weight = [];
        foreach ($active_partner_ids as $partner_id) {
            $partner_level_id = $this->user_cal[$partner_id]['data']['partner_level_id'] ?? 0;
            $weight = $this->arr_partner_levels[$partner_level_id]['orderform_ad_weight'] ?? 0;
            $total_weight += $weight;
            $user_weight[$partner_id] = $weight;
        }
        return [
            'total_weight' => $total_weight,
            'user_weight' => $user_weight,
        ];
    }

    /**
     * 組織有「增值積分」的會員並回傳會員ids
     */
    public function get_accounts_has_pi()
    {
        $pi_user_ids = [];
        /*撈取有增值積分、非系統、飛月分紅的會員*/
        //TODO: 有增加帳戶要記得加在這裡
        $users = Db::connection('main_db')->table('account')
            ->where('point_increasable', '>', 0)
            ->where('id', '!=', config('extra.skychakra.member_system'))
            ->where('id', '!=', config('extra.skychakra.member_month_divided'))
            ->get();

        $users = CommonService::objectToArray($users);

        foreach ($users as $user) {
            $this->init_user_set($user['id'], $user);
            array_push($pi_user_ids, $user['id']);
        }

        return $pi_user_ids;
    }



    /*-------------------------------------------------------------*/
    /*以下是其他會異動到資金池的積分運算相關操作-----------------------*/
    /*外部呼叫後僅做設定，最終一定要呼叫 send_by_cal 才會實際調整------*/

    /*增值積分轉換現金積分(依傳入會員編號、轉換點數量、轉換訊息 處理)*/
    public function set_point_increasable_to_point(string $from = null, int $num = null, string $msg)
    {
        $account_from = Db::connection('main_db')->table('account')->where('number', '=', $from)->first();
        if (!$account_from) {
            throw new \Exception(Lang::get('無此會員'));
        } else if ($account_from->id == config('extra.skychakra.member_month_divided')) {
            throw new \Exception(Lang::get('無此會員'));
        } else if ($account_from->increasing_limit_invest < 0) {
            throw new \Exception(Lang::get('累積自動升級中，無法處理'));
        }
        if (!$num) {
            throw new \Exception(Lang::get('未設定轉移數量'));
        }
        if ($num < 0) {
            throw new \Exception(Lang::get('轉移數量不可小餘0'));
        } else if ($num > $account_from->point_increasable) {
            throw new \Exception(Lang::get('會員增值積分數量不足'));
        }
        if (!$msg) {
            throw new \Exception(Lang::get('未設定轉移說明'));
        }

        $this->init_user_set($account_from->id);  /*初始化此會員*/
        $this->point_increasable_to_point($account_from->id, $num, $msg);
    }

    /*現金積分提現(依傳入會員ID、使用現金積分數量 處理本次資金異動等設定)*/
    public function point_to_cash(int $target_id, string $currency, float $point_num, string $msg)
    {
        /*初始化此會員*/
        $this->init_user_set($target_id);
        if (!isset($this->user_cal[$target_id])) {
            throw new \Exception(Lang::get('無此會員'));
        } else if ($point_num <= 0) {
            throw new \Exception(Lang::get('請輸入整數'));
        } else if ($point_num > $this->user_cal[$target_id]['data']['point']) {
            throw new \Exception(Lang::get('超過持有現金積分上限'));
        }

        /*設定使用現金積分*/
        $point_change = (float)(-1.0 * $point_num);
        $this->set_final_points_record($target_id, $msg, $point_change);
        /*計算代扣稅金*/
        $tax_num = $point_num * $this->bonus_setting['charge_tax'];
        /*計算回歸資金池金額*/
        $pool_num = $point_num * $this->bonus_setting['charge_pool'];
        /*實領點數*/
        $cash_point = $point_num - $tax_num - $pool_num;

        /*依「回歸資金池金額」增加總計「增值積分資金池」CV金額*/
        $this->add_pi_pool($pool_num);

        /*依「實領點數」添加提現紀錄*/
        $this->MemberInstance->change_user_id($target_id);
        $this->MemberInstance->add_point_to_cash_record($currency, $cash_point);
    }

    /*依傳入會員ID 將全部增值積分轉移到現金積分*/
    public function clean_point_increasable(int $target_id)
    {
        if (!isset($this->user_cal[$target_id])) {
            throw new \Exception(Lang::get('無此會員'));
        }
        $pi_value_ori = $this->get_pi_value();

        /*目前會員的增值積分*/
        $point_increasable = $this->user_cal[$target_id]['data']['point_increasable'];
        if ($point_increasable > 0) {
            $this->point_increasable_to_point($target_id, $point_increasable, '個人責任額檢查未達標(拋轉現金積分)');
        }
    }

    /*依傳入會員ID 將全部消費圓滿點數、其他圓滿點數清零*/
    public function clean_increasing_limit_consumption_and_other(int $target_id)
    {
        if (!isset($this->user_cal[$target_id])) {
            throw new \Exception(Lang::get('無此會員'));
        }

        // 清空消費圓滿點數
        $increasing_limit_consumption = $this->user_cal[$target_id]['data']['increasing_limit_consumption'];
        if ($increasing_limit_consumption > 0) {
            $num = (float)(-1.0 * $increasing_limit_consumption);
            $this->set_final_increasing_limit_record($target_id, '個人責任額檢查未達標(清空消費圓滿點數)', $num, 2, 3);
        }

        // 與天脈202241212會議決定不要
        // // 清空其他圓滿點數
        // $increasing_limit_other = $this->user_cal[$target_id]['data']['increasing_limit_other'];
        // if($increasing_limit_other>0){
        //   $num = (float)(-1.0 * $increasing_limit_other);
        //   $this->set_final_increasing_limit_record($target_id, '個人責任額檢查未達標(清空其他圓滿點數)', $num, 3, 3);
        // }
    }
}
